import os

CONFIG = {
    # ====== 🚀 核心配置 ======
    'max_test_samples': 1500,
    'embedding_model_path': 'BAAI/bge-m3',

    # ====== 🔗 API配置 ======
    'base_url': 'http://8.138.94.162:3001/proxy/silicon/v1',
    'model_name': 'Qwen/Qwen2.5-7B-Instruct',
    'api_key': 'sk-zhongyushi',
    'max_concurrent_requests': 500,
    'batch_size': 64,
    'embedding_batch_size': 64,
    'batch_delay': 1.0,
    'timeout_seconds': 600,
    'api_timeout': 300,
    'stage2_timeout': 1200,
    'stage3_timeout': 1800,
    'max_retries': 3,
    'retry_delay': 5,

    # ====== 📊 界面配置 ======
    'log_level': 'WARNING',

    # ====== 📁 路径配置 ======
    'cache_dir': './cache',
    'data_root_dir': './data',

    # ====== 🔍 检索配置 ======
    'retrieval_config': {
        'vector_top_k': 30,
        'reranker_top_k': 10,
        'final_examples_count': 3,
        'score_threshold': 0.1,
        'diversity_lambda': 0.3,
        'fallback_examples_count': 2,
    },

    # ====== 📊 数据集配置 ======
    'current_dataset': 'conll2003',
    'datasets': {
        'ace2005': {
            'name': 'ACE 2005',
            'path': 'data/ACE 2005/train.json',
            'labels': ['person', 'organization', 'location', 'facility', 'weapon', 'vehicle', 'geo-political'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from ACE 2005 dataset:**
- person: People, individuals, groups (e.g., "John Smith", "Mary Johnson", "the team")
- organization: Companies, institutions, agencies (e.g., "Apple Inc.", "Microsoft", "FBI")
- location: Places, addresses, geographic locations (e.g., "New York", "California", "Main Street")
- facility: Buildings, structures, installations (e.g., "White House", "airport", "hospital")
- weapon: Weapons, armaments (e.g., "rifle", "missile", "bomb")
- vehicle: Transportation vehicles (e.g., "car", "airplane", "ship")
- geo-political: Geographic and political entities (e.g., "United States", "European Union", "NATO")"""
        },
        'conll2003': {
            'name': 'CoNLL 2003',
            'path': 'data/CoNLL2003/train.json',
            'labels': ['PER', 'ORG', 'LOC', 'MISC'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from CoNLL 2003 dataset:**
- PER: Person names (e.g., "John Smith", "Mary Johnson")
- ORG: Organizations (e.g., "Apple Inc.", "Microsoft")
- LOC: Locations (e.g., "New York", "California")
- MISC: Miscellaneous named entities"""
        }
    }
}

# ====== 🛠️ 工具函数 ======
def set_dataset(dataset_key: str) -> bool:
    """切换数据集"""
    if dataset_key in CONFIG['datasets']:
        CONFIG['current_dataset'] = dataset_key
        return True
    return False

def get_current_dataset_info() -> dict:
    """获取当前数据集信息"""
    return CONFIG['datasets'][CONFIG['current_dataset']]

def get_current_dataset_path() -> str:
    """获取当前数据集路径"""
    return get_current_dataset_info()['path']

def get_current_label_prompt() -> str:
    """获取当前数据集的标签prompt"""
    return get_current_dataset_info()['label_prompt']

def list_available_datasets() -> dict:
    """列出所有可用数据集"""
    available = {}
    for key, info in CONFIG['datasets'].items():
        available[key] = {
            'name': info['name'],
            'available': os.path.exists(info['path']),
            'current': key == CONFIG['current_dataset'],
            'labels': info.get('labels', [])
        }
    return available





def get_dataset_cache_dir() -> str:
    """获取当前数据集的缓存目录"""
    dataset_name = get_current_dataset_info()['name'].lower().replace(' ', '_')
    cache_dir = CONFIG['cache_dir']
    dataset_cache_dir = f"{cache_dir}/{dataset_name}"
    os.makedirs(dataset_cache_dir, exist_ok=True)
    return dataset_cache_dir

def get_cache_path(cache_type: str) -> str:
    """获取缓存文件路径"""
    dataset_cache_dir = get_dataset_cache_dir()
    return f"{dataset_cache_dir}/{cache_type}.json"


def initialize_datasets():
    """初始化数据集配置"""
    print("🚀 初始化数据集配置...")
    print("✅ 数据集配置已加载")



