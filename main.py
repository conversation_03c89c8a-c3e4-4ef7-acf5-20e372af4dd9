#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理版本)
阶段1: 统一生成检索请求
阶段2: 统一检索
阶段3: 统一NER
"""

import asyncio
import argparse
import logging
import json

import os
from typing import Dict, Any, Optional, List, Type
from datetime import datetime

# 尝试导入异步文件操作库
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    print("⚠️ aiofiles未安装，将使用同步文件操作。建议安装: pip install aiofiles")

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets, get_cache_path
from schemas import RetrieveNERExamplesTool
from pydantic import BaseModel

# 常量定义
DEFAULT_RETRIEVAL_DESCRIPTION = "general NER examples"


async def async_read_json(file_path: str) -> Any:
    """异步读取JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
    else:
        # 回退到线程池中的同步操作，避免阻塞事件循环
        def _sync_read():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        # Python 3.8兼容性：使用run_in_executor替代to_thread
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            return await asyncio.get_event_loop().run_in_executor(executor, _sync_read)


async def async_write_json(file_path: str, data: Any) -> None:
    """异步写入JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            content = json.dumps(data, ensure_ascii=False, indent=2)
            await f.write(content)
    else:
        # 回退到线程池中的同步操作，避免阻塞事件循环
        def _sync_write():
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        # Python 3.8兼容性：使用run_in_executor替代to_thread
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            await asyncio.get_event_loop().run_in_executor(executor, _sync_write)


def setup_logging(level: str = "WARNING"):
    """设置日志配置"""
    import sys
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        stream=sys.stderr
    )


import threading


class ProgressManager:
    """🎯 进度管理器 - 美化和管理进度显示的逻辑"""

    def __init__(self):
        self._lock = threading.Lock()
        self._current_stage = None
        self._stage_name = ""
        self._total_tasks = 0
        self._completed = 0
        self._success = 0
        self._failed = 0
        self._start_time = None
        self._spinner_frame = 0
        self._animation_frame = 0

        # 旋转器字符
        self._spinner_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']

        # ANSI转义序列
        self._CLEAR_LINE = '\033[2K'
        self._CURSOR_UP = '\033[1A'
        self._CURSOR_DOWN = '\033[1B'
        self._SAVE_CURSOR = '\033[s'
        self._RESTORE_CURSOR = '\033[u'

    def start_stage(self, stage_name: str, total_tasks: int = 0):
        """开始一个新阶段"""
        with self._lock:
            self._stage_name = stage_name
            self._total_tasks = total_tasks
            self._completed = 0
            self._success = 0
            self._failed = 0
            self._start_time = datetime.now()
            self._current_stage = True

            # 显示阶段开始信息
            print(f"\n{stage_name}")
            if total_tasks > 0:
                self._render_progress()

    def update_progress(self, completed: Optional[int] = None, success: Optional[int] = None,
                       failed: Optional[int] = None, increment: bool = False):
        """更新进度"""
        with self._lock:
            if not self._current_stage:
                return

            if increment:
                # 增量模式
                if completed is not None:
                    self._completed += completed
                if success is not None:
                    self._success += success
                if failed is not None:
                    self._failed += failed
            else:
                # 绝对值模式
                if completed is not None:
                    self._completed = completed
                if success is not None:
                    self._success = success
                if failed is not None:
                    self._failed = failed

            self._render_progress()

    def finish_stage(self, final_message: Optional[str] = None):
        """结束当前阶段"""
        with self._lock:
            if not self._current_stage:
                return

            self._current_stage = False

            # 显示最终进度
            self._render_progress(final=True)

            # 显示完成信息
            if final_message:
                print(f"✅ {final_message}")

            # 计算耗时
            if self._start_time:
                duration = datetime.now() - self._start_time
                print(f"⏱️  耗时: {duration.total_seconds():.1f}秒")

            print()  # 额外换行

    def _render_progress(self, final: bool = False, width: int = 50):
        """渲染进度条"""
        if self._total_tasks <= 0:
            # 无总数的简单进度显示
            if final:
                spinner = "✅"
            else:
                self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
                spinner = self._spinner_chars[self._spinner_frame]

            status_parts = []
            if self._completed > 0:
                status_parts.append(f"已完成: {self._completed}")
            if self._success > 0:
                status_parts.append(f"成功: {self._success}")
            if self._failed > 0:
                status_parts.append(f"失败: {self._failed}")

            status = " | ".join(status_parts) if status_parts else "处理中..."
            print(f"\r{spinner} {status}", end='', flush=True)

            if final:
                print()  # 完成时换行
            return

        # 有总数的详细进度条
        percentage = self._completed / self._total_tasks if self._total_tasks > 0 else 0
        filled = int(width * percentage)

        # 动态效果：让已填充部分有流动效果
        if not final and self._completed < self._total_tasks and filled > 0:
            self._animation_frame = (self._animation_frame + 1) % 3
            if self._animation_frame == 0:
                bar = '█' * filled + '░' * (width - filled)
            elif self._animation_frame == 1:
                bar = '▓' * filled + '░' * (width - filled)
            else:
                bar = '▒' * filled + '░' * (width - filled)
        else:
            # 完成时显示实心
            bar = '█' * filled + '░' * (width - filled)

        # 旋转器
        if final:
            spinner = "✅"
        else:
            self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
            spinner = self._spinner_chars[self._spinner_frame]

        # 构建状态信息
        status_parts = [f"{self._completed}/{self._total_tasks}"]
        if self._success > 0:
            status_parts.append(f"成功: {self._success}")
        if self._failed > 0:
            status_parts.append(f"失败: {self._failed}")

        status = " | ".join(status_parts)

        # 显示进度条
        print(f"\r{spinner} [{bar}] {percentage:.1%} ({status})", end='', flush=True)

        if final:
            print()  # 完成时换行

    def log_message(self, message: str):
        """在不干扰进度条的情况下输出日志信息"""
        with self._lock:
            if self._current_stage:
                # 清除当前进度条，输出消息，然后在新行重新渲染进度条
                print(f"\r{self._CLEAR_LINE}", end="")  # 清除当前行
                print(message)  # 输出消息并自动换行
                self._render_progress()
            else:
                print(message)


def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧠 APIICL - 元认知智能体NER系统 (三阶段处理)")
    print("📚 阶段1: 生成检索请求 → 阶段2: 执行检索 → 阶段3: 执行NER")
    print("=" * 60)


async def _safe_cancel_tasks(tasks: List[asyncio.Task]) -> None:
    """安全取消任务列表"""
    cancelled_tasks = []
    for task in tasks:
        if not task.done():
            task.cancel()
            cancelled_tasks.append(task)

    # 等待被取消的任务完成清理
    if cancelled_tasks:
        await asyncio.gather(*cancelled_tasks, return_exceptions=True)


async def _safe_cleanup_tasks(tasks: List[asyncio.Task]) -> None:
    """确保任务完全清理"""
    if not tasks:
        return

    # 取消所有未完成的任务
    for task in tasks:
        if not task.done():
            task.cancel()

    # 等待所有任务完成（包括清理）
    await asyncio.gather(*tasks, return_exceptions=True)


async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 三阶段统一处理数据集"""

    # 创建进度管理器
    progress = ProgressManager()

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 阶段0：数据准备和向量库预初始化
    progress.start_stage("📚 阶段0：数据准备和向量库预初始化", 3)

    try:
        test_data = await async_read_json(test_path)
        progress.update_progress(completed=1)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]
    progress.update_progress(completed=2)

    # 预初始化向量库
    progress.log_message("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        progress.log_message("✅ 向量库预初始化成功")
    else:
        progress.log_message("⚠️ 向量库预初始化失败，将使用直接模式")

    progress.update_progress(completed=3)
    progress.finish_stage("数据准备阶段完成")
    
    # 初始化元认知智能体
    from meta_cognitive_agent import get_meta_cognitive_agent
    agent = get_meta_cognitive_agent(global_retriever)
    
    # 阶段1：为每个样本生成检索请求
    progress.start_stage("🧠 阶段1：生成检索请求", len(test_data))

    # 检查缓存
    cache_file = get_cache_path(f"requests_{len(test_data)}")

    if os.path.exists(cache_file):
        progress.log_message("📦 发现检索请求缓存，正在加载...")
        try:
            all_retrieval_requests = await async_read_json(cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(all_retrieval_requests)} 个检索请求")
            progress.update_progress(completed=len(all_retrieval_requests))
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 缓存文件损坏，重新生成... 错误: {e}")
            all_retrieval_requests = None
    else:
        all_retrieval_requests = None

    if all_retrieval_requests is None:
        async def generate_single_request(i, sample):
            """并发生成单个检索请求"""
            text = sample.get('text', '')

            try:
                # 生成检索请求
                stage1_prompt = agent._build_stage1_prompt(text)
                tools: List[Type[BaseModel]] = [RetrieveNERExamplesTool]
                messages = [{"role": "user", "content": stage1_prompt}]

                response = await agent.model_service.generate_with_tools_async(
                    messages=messages,
                    tools=tools
                )

                if response and hasattr(response, 'tool_calls') and response.tool_calls:
                    for tool_call in response.tool_calls:
                        if tool_call.function.name == "RetrieveNERExamplesTool":
                            try:
                                arguments = json.loads(tool_call.function.arguments)
                                description = arguments.get("description", "")
                                k = arguments.get("k", 3)
                                return (i, description, k)
                            except (json.JSONDecodeError, KeyError) as e:
                                return (i, DEFAULT_RETRIEVAL_DESCRIPTION, 3)

                return (i, DEFAULT_RETRIEVAL_DESCRIPTION, 3)

            except Exception as e:
                print(f"⚠️ 样本 {i} 生成检索请求失败: {e}")
                return (i, DEFAULT_RETRIEVAL_DESCRIPTION, 3)

        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)
        all_batch_tasks = []

        total_batches = (len(test_data) + batch_size - 1) // batch_size

        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))
            batch_num = i//batch_size + 1



            # 创建批次任务（不等待完成）
            batch_tasks = [
                generate_single_request(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 添加到总任务列表
            all_batch_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(test_data):
                await asyncio.sleep(batch_delay)



        # 使用as_completed显示实时进度
        all_results = []
        completed_count = 0

        for completed_task in asyncio.as_completed(all_batch_tasks):
            result = await completed_task
            all_results.append(result)
            completed_count += 1
            progress.update_progress(completed=completed_count)

        # 处理所有结果
        all_retrieval_requests = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 任务失败: {result}")
                failed_count += 1
            else:
                all_retrieval_requests.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段1完成 - 成功: {len(all_retrieval_requests)}/{len(test_data)}")

        # 按样本ID排序，保持顺序
        all_retrieval_requests.sort(key=lambda x: x[0])

        # 清理不再需要的大型列表，释放内存
        del all_batch_tasks, all_results
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存到缓存
        try:
            await async_write_json(cache_file, all_retrieval_requests)
            print(f"💾 检索请求已缓存到: {cache_file}")
        except Exception as e:
            print(f"⚠️ 缓存保存失败: {e}")
    
    print()
    
    # 阶段2：为每个样本执行检索
    progress.start_stage("🔍 阶段2：执行检索", len(all_retrieval_requests))

    # 检查检索结果缓存
    examples_cache_file = get_cache_path(f"examples_{len(test_data)}")

    if os.path.exists(examples_cache_file):
        progress.log_message("📦 发现检索结果缓存，正在加载...")
        try:
            all_examples = await async_read_json(examples_cache_file)
            # 转换字符串键为整数键
            all_examples = {int(k): v for k, v in all_examples.items()}
            progress.log_message(f"✅ 从缓存加载了 {len(all_examples)} 个检索结果")
            progress.update_progress(completed=len(all_examples))
            progress.finish_stage(f"阶段2完成 - 从缓存加载: {len(all_examples)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 检索缓存文件损坏，重新检索... 错误: {e}")
            all_examples = None
    else:
        all_examples = None

    if all_examples is None:
        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)

        async def retrieve_single_example(sample_id, description, k):
            """并发检索单个示例"""
            try:
                examples = await agent._simple_retrieval(description, k)
                return (sample_id, examples)
            except Exception as e:
                progress.log_message(f"⚠️ 样本 {sample_id} 检索失败: {e}")
                return (sample_id, [])

        all_examples = {}

        all_retrieval_tasks = []
        total_batches = (len(all_retrieval_requests) + batch_size - 1) // batch_size

        for i in range(0, len(all_retrieval_requests), batch_size):
            batch_requests = all_retrieval_requests[i:i+batch_size]
            batch_num = i//batch_size + 1



            # 创建批次任务（不等待完成）
            batch_tasks = [
                retrieve_single_example(sample_id, description, k)
                for sample_id, description, k in batch_requests
            ]

            # 添加到总任务列表
            all_retrieval_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(all_retrieval_requests):
                await asyncio.sleep(batch_delay)



        # 创建任务对象以便跟踪进度
        tasks = [asyncio.create_task(coro) for coro in all_retrieval_tasks]
        all_results = []
        completed_count = 0

        try:
            # 使用as_completed显示实时进度，增加超时时间
            stage2_timeout = CONFIG.get('stage2_timeout', 1200)
            for completed_task in asyncio.as_completed(tasks, timeout=stage2_timeout):
                try:
                    result = await completed_task
                    all_results.append(result)
                except Exception as e:
                    all_results.append(e)

                completed_count += 1
                progress.update_progress(completed=completed_count)

        except asyncio.TimeoutError:
            progress.log_message("⚠️ 阶段2任务超时，使用部分结果继续")
            # 安全取消未完成的任务
            await _safe_cancel_tasks(tasks)
            # 使用已完成的结果
            if len(all_results) < len(tasks):
                all_results.extend([Exception("Timeout")] * (len(tasks) - len(all_results)))

        finally:
            # 确保所有任务都被清理
            await _safe_cleanup_tasks(tasks)

        # 处理所有结果
        all_examples = {}
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 检索任务失败: {result}")
                failed_count += 1
            else:
                sample_id, examples = result
                all_examples[sample_id] = examples
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段2完成 - 成功: {len(all_examples)}/{len(all_retrieval_requests)}")

        # 清理不再需要的大型列表，释放内存
        del all_retrieval_tasks, tasks, all_results
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存检索结果到缓存
        try:
            await async_write_json(examples_cache_file, all_examples)
            progress.log_message(f"💾 检索结果已缓存到: {examples_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ 检索缓存保存失败: {e}")
    
    print()
    
    # 阶段3：为每个样本执行NER
    progress.start_stage("🎯 阶段3：执行NER", len(test_data))

    # 检查NER结果缓存
    ner_cache_file = get_cache_path(f"ner_results_{len(test_data)}")

    if os.path.exists(ner_cache_file):
        progress.log_message("📦 发现NER结果缓存，正在加载...")
        try:
            cached_results = await async_read_json(ner_cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(cached_results)} 个NER结果")
            results = cached_results
            progress.update_progress(completed=len(cached_results))
            progress.finish_stage(f"阶段3完成 - 从缓存加载: {len(cached_results)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ NER缓存文件损坏，重新执行NER... 错误: {e}")
            results = None
    else:
        results = None

    if results is None:
        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)

        async def execute_single_ner(i, sample):
            """并发执行单个NER"""
            text = sample.get('text', '')
            true_labels = sample.get('label', {})
            examples = all_examples.get(i, [])

            try:
                # 执行NER
                predicted_labels = await agent._execute_ner_stage(text, examples)
            except Exception as e:
                print(f"⚠️ 样本 {i} NER失败: {e}")
                predicted_labels = {}

            if not isinstance(predicted_labels, dict):
                predicted_labels = {}

            # 计算指标
            sample_correct = 0
            sample_total = sum(len(entities) for entities in true_labels.values())
            sample_predicted = sum(len(entities) for entities in predicted_labels.values())

            for entity_type, true_entities in true_labels.items():
                predicted_entities_of_type = predicted_labels.get(entity_type, [])
                for entity in true_entities:
                    if entity in predicted_entities_of_type:
                        sample_correct += 1

            return {
                'text': text,
                'true_labels': true_labels,
                'predicted_labels': predicted_labels,
                'correct': sample_correct,
                'total_true': sample_total,
                'total_predicted': sample_predicted
            }

        results = []

        all_ner_tasks = []
        total_batches = (len(test_data) + batch_size - 1) // batch_size

        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))
            batch_num = i//batch_size + 1



            # 创建批次任务（不等待完成）
            batch_tasks = [
                execute_single_ner(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 添加到总任务列表
            all_ner_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(test_data):
                await asyncio.sleep(batch_delay)



        # 创建任务对象以便跟踪进度
        tasks = [asyncio.create_task(coro) for coro in all_ner_tasks]
        all_results = []
        completed_count = 0

        try:
            # 使用as_completed显示实时进度，增加超时时间
            stage3_timeout = CONFIG.get('stage3_timeout', 1800)
            for completed_task in asyncio.as_completed(tasks, timeout=stage3_timeout):
                try:
                    result = await completed_task
                    all_results.append(result)
                except Exception as e:
                    all_results.append(e)

                completed_count += 1
                progress.update_progress(completed=completed_count)

        except asyncio.TimeoutError:
            progress.log_message("⚠️ 阶段3任务超时，使用部分结果继续")
            # 安全取消未完成的任务
            await _safe_cancel_tasks(tasks)
            # 使用已完成的结果
            if len(all_results) < len(tasks):
                all_results.extend([Exception("Timeout")] * (len(tasks) - len(all_results)))

        finally:
            # 确保所有任务都被清理
            await _safe_cleanup_tasks(tasks)

        # 处理所有结果
        results = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ NER任务失败: {result}")
                failed_count += 1
            else:
                results.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段3完成 - 成功: {len(results)}/{len(test_data)}")

        # 清理不再需要的大型列表，释放内存
        del all_ner_tasks, tasks, all_results
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存NER结果到缓存
        try:
            await async_write_json(ner_cache_file, results)
            progress.log_message(f"💾 NER结果已缓存到: {ner_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ NER缓存保存失败: {e}")

    # 计算汇总指标
    correct_predictions = sum(r['correct'] for r in results)
    total_entities = sum(r['total_true'] for r in results)
    predicted_entities = sum(r['total_predicted'] for r in results)
    
    print()

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 处理样本数: {len(test_data)}")
    print(f"🎯 真实实体总数: {total_entities}")
    print(f"🔍 预测实体总数: {predicted_entities}")
    print(f"✅ 正确预测数: {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'samples_count': len(test_data),
        'total_entities': total_entities,
        'predicted_entities': predicted_entities,
        'correct_predictions': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'processing_mode': 'unified_three_stage',
        'detailed_results': results
    }
    
    # 保存到文件
    results_dir = CONFIG.get('results_dir', './results')
    os.makedirs(results_dir, exist_ok=True)
    eval_file = os.path.join(results_dir, f"eval_results_unified_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        await async_write_json(eval_file, eval_results)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 三阶段统一处理"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理)')
    parser.add_argument('--dataset', '-d', type=str, default=CONFIG.get('dataset', 'ace2005'),
                       help=f'数据集名称 (默认: {CONFIG.get("dataset", "ace2005")})')
    parser.add_argument('--max-samples', type=int, default=CONFIG.get('max_test_samples'),
                       help=f'最大测试样本数 (默认: {CONFIG.get("max_test_samples", "处理全部")})')
    parser.add_argument('--log-level', type=str, default=CONFIG.get('log_level', 'WARNING'),
                       help=f'日志级别 (默认: {CONFIG.get("log_level", "WARNING")}) (DEBUG/INFO/WARNING/ERROR)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print()
    
    try:
        # 执行三阶段统一处理
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
