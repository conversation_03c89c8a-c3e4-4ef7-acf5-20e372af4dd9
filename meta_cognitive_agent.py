#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import asyncio
import logging
import json
import re
from typing import Dict, List, Any

from config import get_current_dataset_info
from model_interface import model_service
from schemas import RetrieveNERExamplesTool

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""
    
    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体

        Args:
            example_retriever: 预初始化的示例检索器
        """
        if example_retriever is None:
            from example_retriever import example_retriever as default_retriever
            self.example_retriever = default_retriever
        else:
            self.example_retriever = example_retriever
        self.model_service = model_service
        self._initialization_started = False
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')

    def _build_stage1_prompt(self, text: str) -> str:
        """
        构建Stage 1的prompt - 让LLM分析文本并生成检索请求
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""Find relevant examples to help extract entities from this text.

Entity types: {entity_types_str}
Text: "{text}"

Call retrieve_ner_examples tool with:
- description: Brief description focusing on text characteristics like style, domain, and complexity (20-30 words)
- k: Number of examples (2-3 recommended for better coverage)

Focus on text features: formal/informal style, domain (news/conversation/technical), sentence complexity.

Call the tool now."""

    async def _ensure_initialized(self):
        """确保示例检索器已初始化"""
        if not self._initialization_started and self.example_retriever and not self.example_retriever.initialized:
            self._initialization_started = True
            logger.info("🔧 自动初始化示例检索器...")
            await self.example_retriever.initialize_vector_store()

    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        🚀 两阶段NER流程

        Stage 1: LLM接受input并做function call，用description和k调用检索器函数得到few-shot
        Stage 2: 用这些few-shot拼接成为prompt做NER
        """
        try:
            await self._ensure_initialized()

            logger.info(f"🧠 Stage 1: 分析文本并生成检索请求: '{text[:50]}...'")

            # Stage 1: LLM分析文本并生成检索请求
            stage1_prompt = self._build_stage1_prompt(text)
            tools = [RetrieveNERExamplesTool]  # 只提供检索工具
            messages = [{"role": "user", "content": stage1_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行检索获取few-shot
                few_shot_examples = await self._execute_retrieval_stage(response.tool_calls)

                if few_shot_examples:
                    logger.info(f"🧠 Stage 2: 基于{len(few_shot_examples)}个示例进行NER")
                    # Stage 2: 基于few-shot进行NER
                    return await self._execute_ner_stage(text, few_shot_examples)
                else:
                    logger.warning("❌ 未获取到few-shot示例")
                    return {}
            else:
                logger.warning("❌ LLM未调用检索工具")
                return {}

        except Exception as e:
            logger.error(f"两阶段NER失败: {e}")
            return {}

    async def _execute_retrieval_stage(self, tool_calls: List[Any]) -> List[Any]:
        """
        执行Stage 1的检索阶段
        """
        for tool_call in tool_calls:
            if not tool_call.function:
                continue

            function_name = tool_call.function.name

            if function_name == "RetrieveNERExamplesTool":
                try:
                    arguments = json.loads(tool_call.function.arguments)
                    description = arguments.get("description", "")
                    k = arguments.get("k", 3)

                    logger.info(f"🔍 检索请求: {description[:50]}..., k={k}")

                    # 执行检索
                    examples = await self._simple_retrieval(description, k)
                    logger.info(f"✅ 检索到 {len(examples)} 个示例")
                    return examples

                except json.JSONDecodeError as e:
                    logger.error(f"解析检索参数失败: {e}")

                    # 尝试修复常见的JSON问题
                    try:
                        # 修复常见的转义问题
                        fixed_json = tool_call.function.arguments.replace('\\', '\\\\')
                        arguments = json.loads(fixed_json)
                        description = arguments.get("description", "")
                        k = arguments.get("k", 3)
                        logger.info(f"✅ JSON修复成功，继续检索: {description[:50]}..., k={k}")

                        examples = await self._simple_retrieval(description, k)
                        logger.info(f"✅ 检索到 {len(examples)} 个示例")
                        return examples

                    except Exception:
                        # 使用默认参数作为降级方案
                        logger.info("🔄 使用默认参数进行降级检索")
                        examples = await self._simple_retrieval("general NER examples", 3)
                        return examples

        return []

    async def _execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        执行Stage 2的NER阶段 - 基于few-shot示例进行NER
        """
        # 构建包含few-shot的prompt
        examples_text = self._format_examples_for_context(few_shot_examples)
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)



        ner_prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below.

OUTPUT FORMAT: Return a JSON object where keys are entity types and values are arrays of entity strings.
- If no entities are found: {{}}
OUTPUT: Valid JSON only. No explanations.

Label set: {entity_types_str}

Examples (learn from these patterns):
{examples_text}

Now extract entities from: "{text}" """

        # 调用LLM进行NER（纯文本生成，不是Function Call）
        messages = [{"role": "user", "content": ner_prompt}]

        response = await self.model_service.generate_simple_async(
            messages=messages,
            temperature=0.1
        )

        if response:
            try:
                # 解析JSON结果
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    entities_json = json_match.group()
                    entities = json.loads(entities_json)
                    logger.info(f"✅ Stage 2完成，提取到 {sum(len(v) for v in entities.values())} 个实体")
                    return entities
                else:
                    logger.warning("❌ 无法从响应中提取JSON")
                    return {}
            except json.JSONDecodeError as e:
                logger.error(f"解析NER结果失败: {e}")
                return {}
        else:
            logger.warning("❌ Stage 2 NER失败")
            return {}

    async def _simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        🔍 简化的检索方法 - 直接基于description检索k个示例
        """
        try:
            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            # 直接使用description和k进行检索
            examples = await self.example_retriever.simple_retrieve(description, k)
            return examples

        except Exception as e:
            logger.error(f"简化检索失败: {e}")
            return []

    def _format_examples_for_context(self, examples) -> str:
        """将检索到的示例格式化为上下文"""
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            # 检查是否是ScoredExample对象
            if hasattr(example, 'example'):
                # ScoredExample对象
                example_data = example.example
            else:
                # 普通字典
                example_data = example

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            entities_str = ", ".join(
                f"'{entity}' ({etype})"
                for etype, entities in labels.items()
                for entity in entities
            )

            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)


# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
